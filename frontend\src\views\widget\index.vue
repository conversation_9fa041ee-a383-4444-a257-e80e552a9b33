<template>
  <div class="widget-layout-editor">
    <div class="content pt-4">
      <!-- 画布区域 -->
      <div class="canvas-container">
        <div class="canvas-wrapper">
          <div class="title text-[30px]">组件布局调整</div>
          <canvas id="widgetCanvas" ref="canvasRef"></canvas>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="control-panel">
        <v-card class="pa-4">
          <!-- 组件微调 -->
          <div class="fine-tune-panel">
            <h4 class="mb-4">组件微调: {{ getWidgetLabel(selectedWidget) }}</h4>
            <div class="d-flex justify-center align-center flex-column mt-6">
              <v-btn
                icon="mdi-arrow-up"
                size="large"
                @mousedown="startFineTune('up')"
                @mouseup="stopFineTune"
                @mouseleave="stopFineTune"
                @touchstart.prevent="startFineTune('up')"
                @touchend="stopFineTune"
                class="mb-1"
              ></v-btn>
              <div class="d-flex">
                <v-btn
                  icon="mdi-arrow-left"
                  size="large"
                  @mousedown="startFineTune('left')"
                  @mouseup="stopFineTune"
                  @mouseleave="stopFineTune"
                  @touchstart.prevent="startFineTune('left')"
                  @touchend="stopFineTune"
                  class="mr-8"
                ></v-btn>
                <v-btn
                  icon="mdi-arrow-right"
                  size="large"
                  @mousedown="startFineTune('right')"
                  @mouseup="stopFineTune"
                  @mouseleave="stopFineTune"
                  @touchstart.prevent="startFineTune('right')"
                  @touchend="stopFineTune"
                  class="ml-8"
                ></v-btn>
              </div>
              <v-btn
                icon="mdi-arrow-down"
                size="large"
                @mousedown="startFineTune('down')"
                @mouseup="stopFineTune"
                @mouseleave="stopFineTune"
                @touchstart.prevent="startFineTune('down')"
                @touchend="stopFineTune"
                class="mt-1"
              ></v-btn>
            </div>
          </div>

          <!-- 组件列表 -->
          <div class="widget-list mt-6">
            <h4 class="mb-6">组件列表</h4>
            <div v-for="(widget, key) in widgets" :key="key" class="widget-item mb-2">
              <v-checkbox
                v-model="widget.isShow"
                :label="getWidgetLabel(key)"
                @change="updateWidgetVisibility(key)"
                density="compact"
                hide-details
              ></v-checkbox>
            </div>
          </div>
        </v-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, onUnmounted, nextTick } from "vue";
import { Canvas, Rect, FabricText, Line, Group } from "fabric";
import { sendParams2Android, getParamsFromAndroid } from "@/utils/androidMessage";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";
import notificationService from "@/utils/notificationService";

// 响应式数据
const canvasRef = ref(null);
let canvas = null;
const selectedWidget = ref(null);

// --- 智能对齐新增变量 ---
const snapThreshold = 5; // 吸附阈值
let snapLines = []; // 缓存静态组件的对齐线
let activeSnapLines = []; // 当前激活的对齐线
const verticalLines = ref([]); // 用于渲染的垂直辅助线
const horizontalLines = ref([]); // 用于渲染的水平辅助线
// --- 智能对齐新增变量结束 ---

/*
| 宽度 (W) | 高度 (H = W × 9 / 16) |
| -----: | ------------------: |
|    720 |                 405 |
|    960 |                 540 |
|   1200 |                 675 |
|   1440 |                 810 |
 */

// 屏幕尺寸配置
const screenSize = reactive({
  width: 1920,
  height: 1080,
});

// 画布配置
const canvasConfig = reactive({
  width: 960,
  height: 540,
  scale: 960 / 1920, // 缩放比例
});

// 组件数据
const widgets = reactive({
  antiCollision: {
    isShow: true,
    left: 100,
    top: 100,
    width: 200,
    height: 150,
  },
  tooth: {
    isShow: true,
    left: 320,
    top: 100,
    width: 200,
    height: 150,
  },
  posture: {
    isShow: true,
    left: 540,
    top: 100,
    width: 200,
    height: 150,
  },
  handle: {
    isShow: true,
    left: 100,
    top: 270,
    width: 200,
    height: 150,
  },
  deviceInformation: {
    isShow: true,
    left: 320,
    top: 270,
    width: 200,
    height: 150,
  },
});

// 组件标签映射
const widgetLabels = {
  antiCollision: "防碰撞",
  tooth: "斗齿",
  posture: "姿态",
  handle: "手柄",
  deviceInformation: "设备信息",
};

// 获取组件标签
const getWidgetLabel = (key) => {
  return widgetLabels[key] || key;
};

// 初始化画布
const initCanvas = () => {
  canvas = new Canvas("widgetCanvas", {
    width: canvasConfig.width,
    height: canvasConfig.height,
    backgroundColor: "#1a1a1a",
    selection: true,
  });

  // 添加网格
  addGrid();

  // 渲染所有组件
  renderAllWidgets();

  // 监听选择事件
  canvas.on("mouse:down", handleMouseDown);
  canvas.on("selection:cleared", () => {
    selectedWidget.value = null;
  });

  // 监听对象移动事件
  canvas.on("object:moving", handleObjectMoving);
  // 监听对象移动结束事件
  canvas.on("mouse:up", handleMouseUp);
};

// --- 智能对齐新增函数 ---

// 拖拽开始时，缓存所有静态组件的对齐线
const cacheSnapLines = (activeObject) => {
  snapLines = [];
  canvas.getObjects().forEach((obj) => {
    if (obj === activeObject || !obj.widgetKey) return;

    const left = obj.left;
    const top = obj.top;
    const width = obj.getScaledWidth();
    const height = obj.getScaledHeight();

    snapLines.push(
      // 垂直线
      { type: "vertical", position: left, start: top, end: top + height }, // 左
      { type: "vertical", position: left + width / 2, start: top, end: top + height }, // 中
      { type: "vertical", position: left + width, start: top, end: top + height }, // 右
      // 水平线
      { type: "horizontal", position: top, start: left, end: left + width }, // 上
      { type: "horizontal", position: top + height / 2, start: left, end: left + width }, // 中
      { type: "horizontal", position: top + height, start: left, end: left + width } // 下
    );
  });
};

// 绘制辅助线
const drawSnapLines = () => {
  // 移除旧的线
  canvas.getObjects("line").forEach((line) => {
    if (line.snapLine) {
      canvas.remove(line);
    }
  });

  activeSnapLines.forEach((snap) => {
    let fromX, fromY, toX, toY;
    if (snap.type === "vertical") {
      fromX = toX = snap.position;
      fromY = Math.min(snap.objStart, snap.snapStart);
      toY = Math.max(snap.objEnd, snap.snapEnd);
    } else {
      fromY = toY = snap.position;
      fromX = Math.min(snap.objStart, snap.snapStart);
      toX = Math.max(snap.objEnd, snap.snapEnd);
    }
    const line = new Line([fromX, fromY, toX, toY], {
      stroke: "red",
      strokeWidth: 1,
      selectable: false,
      evented: false,
      snapLine: true, // 自定义属性，用于识别
    });
    canvas.add(line);
  });
  canvas.renderAll();
};

// 清除辅助线
const clearSnapLines = () => {
  activeSnapLines = [];
  canvas.getObjects("line").forEach((line) => {
    if (line.snapLine) {
      canvas.remove(line);
    }
  });
  canvas.renderAll();
};

// --- 智能对齐新增函数结束 ---

// 添加网格
const addGrid = () => {
  const gridSize = 20;
  const gridLines = [];

  // 垂直线
  for (let i = 0; i <= canvasConfig.width; i += gridSize) {
    const line = new Line([i, 0, i, canvasConfig.height], {
      stroke: "#424242",
      strokeWidth: 1,
      selectable: false,
      evented: false,
    });
    gridLines.push(line);
  }

  // 水平线
  for (let i = 0; i <= canvasConfig.height; i += gridSize) {
    const line = new Line([0, i, canvasConfig.width, i], {
      stroke: "#424242",
      strokeWidth: 1,
      selectable: false,
      evented: false,
    });
    gridLines.push(line);
  }

  // 添加到画布
  gridLines.forEach((line) => canvas.add(line));
};

// 渲染所有组件
const renderAllWidgets = () => {
  Object.keys(widgets).forEach((key) => {
    if (widgets[key].isShow) {
      createWidgetRect(key);
    }
  });
};

// 创建组件矩形
const createWidgetRect = (widgetKey) => {
  const widget = widgets[widgetKey];

  const rect = new Rect({
    left: widget.left * canvasConfig.scale,
    top: widget.top * canvasConfig.scale,
    width: widget.width * canvasConfig.scale,
    height: widget.height * canvasConfig.scale,
    fill: "rgba(255, 95, 0, 0.3)",
    stroke: "#ff5f00",
    strokeWidth: 2,
    cornerColor: "#ff5f00",
    cornerSize: 8,
    transparentCorners: false,
    widgetKey: widgetKey,
  });

  const text = new FabricText(getWidgetLabel(widgetKey), {
    left: widget.left * canvasConfig.scale + (widget.width * canvasConfig.scale) / 2,
    top: widget.top * canvasConfig.scale + (widget.height * canvasConfig.scale) / 2,
    fontSize: 14,
    fill: "#ffffff",
    textAlign: "center",
    originX: "center",
    originY: "center",
    selectable: false,
    evented: false,
  });

  const group = new Group([rect, text], {
    widgetKey: widgetKey,
    lockRotation: true,
    lockScalingX: true,
    lockScalingY: true,
    hasControls: false,
    borderColor: "#ff5f00",
  });

  canvas.add(group);
};

// 处理选择事件
const handleMouseDown = (e) => {
  const target = e.target;
  if (target && target.widgetKey) {
    selectedWidget.value = target.widgetKey;
    // 缓存对齐线
    cacheSnapLines(target);
  } else {
    selectedWidget.value = null;
  }
};

const handleMouseUp = () => {
  // 拖拽结束时清除辅助线
  clearSnapLines();
};

// 处理对象移动
const handleObjectMoving = (e) => {
  const obj = e.target;
  const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;
  const objWidth = obj.getScaledWidth();
  const objHeight = obj.getScaledHeight();

  // --- 智能对齐逻辑 ---
  activeSnapLines = [];
  let snappedX = false;
  let snappedY = false;

  const objLeft = obj.left;
  const objTop = obj.top;
  const objCenterX = obj.left + objWidth / 2;
  const objCenterY = obj.top + objHeight / 2;
  const objRight = obj.left + objWidth;
  const objBottom = obj.top + objHeight;

  // 检测垂直对齐
  for (const line of snapLines) {
    if (line.type === "vertical") {
      // 左对齐
      if (Math.abs(objLeft - line.position) <= snapThreshold) {
        obj.left = line.position;
        snappedX = true;
        activeSnapLines.push({ type: "vertical", position: line.position, objStart: objTop, objEnd: objBottom, snapStart: line.start, snapEnd: line.end });
        break;
      }
      // 中间对齐
      if (Math.abs(objCenterX - line.position) <= snapThreshold) {
        obj.left = line.position - objWidth / 2;
        snappedX = true;
        activeSnapLines.push({ type: "vertical", position: line.position, objStart: objTop, objEnd: objBottom, snapStart: line.start, snapEnd: line.end });
        break;
      }
      // 右对齐
      if (Math.abs(objRight - line.position) <= snapThreshold) {
        obj.left = line.position - objWidth;
        snappedX = true;
        activeSnapLines.push({ type: "vertical", position: line.position, objStart: objTop, objEnd: objBottom, snapStart: line.start, snapEnd: line.end });
        break;
      }
    }
  }

  // 检测水平对齐
  for (const line of snapLines) {
    if (line.type === "horizontal") {
      // 顶对齐
      if (Math.abs(objTop - line.position) <= snapThreshold) {
        obj.top = line.position;
        snappedY = true;
        activeSnapLines.push({ type: "horizontal", position: line.position, objStart: objLeft, objEnd: objRight, snapStart: line.start, snapEnd: line.end });
        break;
      }
      // 中间对齐
      if (Math.abs(objCenterY - line.position) <= snapThreshold) {
        obj.top = line.position - objHeight / 2;
        snappedY = true;
        activeSnapLines.push({ type: "horizontal", position: line.position, objStart: objLeft, objEnd: objRight, snapStart: line.start, snapEnd: line.end });
        break;
      }
      // 底对齐
      if (Math.abs(objBottom - line.position) <= snapThreshold) {
        obj.top = line.position - objHeight;
        snappedY = true;
        activeSnapLines.push({ type: "horizontal", position: line.position, objStart: objLeft, objEnd: objRight, snapStart: line.start, snapEnd: line.end });
        break;
      }
    }
  }

  drawSnapLines();
  // --- 智能对齐逻辑结束 ---

  // 边界检测
  if (obj.left < 0) obj.left = 0;
  if (obj.top < 0) obj.top = 0;
  if (obj.left + objWidth > canvasWidth) obj.left = canvasWidth - objWidth;
  if (obj.top + objHeight > canvasHeight) obj.top = canvasHeight - objHeight;


  if (obj.widgetKey) {
    // 更新组件位置数据
    widgets[obj.widgetKey].left = Math.round(obj.left / canvasConfig.scale);
    widgets[obj.widgetKey].top = Math.round(obj.top / canvasConfig.scale);

    // 实时应用更改
    applyChanges();
  }
};

// 更新组件可见性
const updateWidgetVisibility = (widgetKey) => {
  // 移除现有对象
  const objects = canvas.getObjects().filter((obj) => obj.widgetKey === widgetKey);
  objects.forEach((obj) => canvas.remove(obj));

  // 如果显示，重新创建
  if (widgets[widgetKey].isShow) {
    createWidgetRect(widgetKey);
  }

  canvas.renderAll();

  // 实时应用更改
  applyChanges();
};

// 更新组件位置
const updateWidgetPosition = () => {
  if (!selectedWidget.value) return;

  const widgetKey = selectedWidget.value;
  const objects = canvas.getObjects().filter((obj) => obj.widgetKey === widgetKey);

  objects.forEach((obj) => {
    obj.set({
      left: widgets[widgetKey].left * canvasConfig.scale,
      top: widgets[widgetKey].top * canvasConfig.scale,
    });
  });

  canvas.renderAll();
};

// 微调组件
const fineTuneInterval = ref(null);

const fineTune = (direction) => {
  if (!selectedWidget.value) return;
  const widget = widgets[selectedWidget.value];
  const step = 1; // 微调步长

  switch (direction) {
    case "up":
      widget.top = Math.max(0, widget.top - step);
      break;
    case "down":
      widget.top = Math.min(screenSize.height - widget.height, widget.top + step);
      break;
    case "left":
      widget.left = Math.max(0, widget.left - step);
      break;
    case "right":
      widget.left = Math.min(screenSize.width - widget.width, widget.left + step);
      break;
  }
  updateWidgetPosition();
};

const startFineTune = (direction) => {
  stopFineTune(); // 先停止任何正在进行的微调
  fineTune(direction); // 立即执行一次
  fineTuneInterval.value = setInterval(() => {
    fineTune(direction);
  }, 100); // 每100毫秒移动一次
};

const stopFineTune = () => {
  if (fineTuneInterval.value) {
    clearInterval(fineTuneInterval.value);
    fineTuneInterval.value = null;
    // 实时应用更改
    applyChanges();
  }
};

// 应用设置
const applyChanges = async () => {
  const payload = {
    screen: {
      width: screenSize.width,
      height: screenSize.height,
    },
    ...widgets,
  };

  await sendParams2Android("controlPosition", JSON.parse(JSON.stringify(payload)));

  console.log("组件布局已应用:", payload);
};

const handleWsMessage = (_, msg) => {
  console.log(msg);

  if (
    msg.jsonData &&
    msg.communicationType === 0 &&
    msg.jsonData.page === 290 &&
    msg.jsonData.payload &&
    msg.jsonData.payload.route === "controlPosition"
  ) {
    const { screen, ...widgetData } = msg.jsonData.payload.value;
    Object.assign(screenSize, screen);
    Object.assign(widgets, widgetData);
    notificationService.success("获取布局数据成功");
  }
};

let handlerId = null;

// 组件挂载
onMounted(async () => {
  handlerId = registerWsHandler(290, handleWsMessage);
  await getParamsFromAndroid("controlPosition", 300);
  await nextTick();
  await initCanvas();
  notificationService.success("获取布局数据成功");
});

// 组件卸载
onBeforeUnmount(() => {
  if (canvas) {
    canvas.dispose();
  }
});

onUnmounted(() => {
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});
</script>

<style scoped>
.widget-layout-editor {
  height: calc(100vh - var(--app-bar-height));
  display: flex;
  flex-direction: column;
  background-color: #0e0e0e;
  color: #ffffff;
  overflow-y: auto;
}


.content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
}

.canvas-container {
  flex: 1;
  background-color: #212121;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border: 1px solid #424242;
}

.canvas-wrapper {
  display: flex;
  flex-direction: column;
  justify-content:space-evenly;
  align-items: center;
  height: 100%;
}

.control-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
}

.control-panel .v-card {
  flex: 1;
}

.widget-item {
  padding: 8px;
  border: 1px solid #424242;
  border-radius: 4px;
  background-color: #212121;
  margin-bottom: 8px;
}

.widget-properties {
  border-top: 1px solid #424242;
  padding-top: 16px;
}

/* 深色主题下的文本颜色 */
.widget-item label,
.widget-properties h4,
.screen-info h4,
.screen-info p {
  color: #ffffff !important;
}

/* 画布背景调整为深色 */
#widgetCanvas {
  border: 1px solid #424242;
  border-radius: 4px;
}
</style>

